import React from 'react';
import { Row, Col, Card, Statistic, Typography, Badge, Descriptions, Button, Space, Progress } from 'antd';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import { useAuthStore } from '@/store/auth';

const { Title, Text } = Typography;

/**
 * 操作员专用仪表板
 * 专注于当前任务、设备状态和个人绩效
 */
const OperatorDashboard: React.FC = () => {
  const { user } = useAuthStore();
  
  const { data: operatorData, isLoading } = useQuery(
    ['operator-dashboard', user?.id],
    () => apiClient.getOperatorDashboard(user?.id || 0),
    {
      refetchInterval: 10000, // 10秒刷新一次
      enabled: !!user?.id
    }
  );

  // 模拟数据 - 实际应该从API获取
  const mockData = {
    currentTask: {
      workOrderNumber: 'WO20241206001',
      partName: '齿轮轴',
      partNumber: 'P123456',
      progress: 15,
      total: 50,
      estimatedCompletion: '14:30',
      priority: 'high'
    },
    myMachines: [
      { id: 1, name: 'CNC-001', status: 'running', skill: 'CNC Machining' },
      { id: 2, name: 'MILL-003', status: 'idle', skill: 'Milling' },
    ],
    dailyStats: {
      completedTasks: 8,
      qualityRate: 98.5,
      efficiency: 95.2,
      workingHours: 7.5
    },
    upcomingTasks: [
      { id: 1, workOrder: 'WO20241206002', part: '轴承座', priority: 'medium', estimatedStart: '15:00' },
      { id: 2, workOrder: 'WO20241206003', part: '连接器', priority: 'low', estimatedStart: '16:30' },
    ]
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'processing';
      case 'idle': return 'default';
      case 'maintenance': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'low': return '#52c41a';
      default: return '#1890ff';
    }
  };

  return (
    <div>
      <div className="page-header" style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          我的工作台
        </Title>
        <Text type="secondary">
          欢迎，{user?.full_name || user?.username} |
          技能组：{user?.skills?.map(skill => {
            const skillMap: { [key: string]: string } = {
              'CNC Machining': 'CNC加工',
              'Milling': '铣削加工',
              'Turning': '车削加工',
              'Grinding': '磨削加工',
              'Assembly': '装配',
              'Quality Control': '质量控制',
              'Packaging': '包装'
            };
            return skillMap[skill] || skill;
          }).join(', ') || '未分配'} |
          今日工作时间：{mockData.dailyStats.workingHours}小时
        </Text>
      </div>

      {/* 当前任务卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <PlayCircleOutlined style={{ color: '#1890ff' }} />
                当前任务
              </Space>
            }
            extra={
              <Badge 
                color={getPriorityColor(mockData.currentTask.priority)} 
                text={mockData.currentTask.priority === 'high' ? '高优先级' : '普通优先级'} 
              />
            }
            className="current-task-card"
          >
            <Row gutter={16}>
              <Col span={16}>
                <Descriptions column={2} size="small">
                  <Descriptions.Item label="工单号">
                    <Text strong>{mockData.currentTask.workOrderNumber}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="零件">
                    {mockData.currentTask.partName} - {mockData.currentTask.partNumber}
                  </Descriptions.Item>
                  <Descriptions.Item label="进度">
                    {mockData.currentTask.progress}/{mockData.currentTask.total} 完成
                  </Descriptions.Item>
                  <Descriptions.Item label="预计完成">
                    <Text type="success">{mockData.currentTask.estimatedCompletion}</Text>
                  </Descriptions.Item>
                </Descriptions>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress 
                    type="circle" 
                    percent={Math.round((mockData.currentTask.progress / mockData.currentTask.total) * 100)}
                    size={80}
                  />
                  <div style={{ marginTop: 8 }}>
                    <Button type="primary" size="large">
                      继续任务
                    </Button>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 统计数据 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日完成任务"
              value={mockData.dailyStats.completedTasks}
              suffix="个"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="质量合格率"
              value={mockData.dailyStats.qualityRate}
              suffix="%"
              prefix={<SafetyCertificateOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="工作效率"
              value={mockData.dailyStats.efficiency}
              suffix="%"
              prefix={<ToolOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="工作时长"
              value={mockData.dailyStats.workingHours}
              suffix="小时"
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 我的设备和待办任务 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <SettingOutlined />
                我的设备
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              {mockData.myMachines.map(machine => (
                <div key={machine.id} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <div>
                    <Text strong>{machine.name}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {machine.skill}
                    </Text>
                  </div>
                  <Badge 
                    status={getStatusColor(machine.status)} 
                    text={machine.status === 'running' ? '运行中' : '空闲'} 
                  />
                </div>
              ))}
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <ClockCircleOutlined />
                待办任务
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              {mockData.upcomingTasks.map(task => (
                <div key={task.id} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <div>
                    <Text strong>{task.workOrder}</Text>
                    <br />
                    <Text type="secondary">{task.part}</Text>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <Badge 
                      color={getPriorityColor(task.priority)} 
                      text={task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低'} 
                    />
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {task.estimatedStart}
                    </Text>
                  </div>
                </div>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default OperatorDashboard;
