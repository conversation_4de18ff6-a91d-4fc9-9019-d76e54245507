import React from 'react';
import { Row, Col, Card, Statistic, Typography, Spin, Alert } from 'antd';
import {
  ProjectOutlined,
  FileTextOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  SettingOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import { useAuthStore } from '@/store/auth';
import { hasRole, ROLES } from '@/utils/permissions';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import OperatorDashboard from '@/components/dashboards/OperatorDashboard';
import QualityInspectorDashboard from '@/components/dashboards/QualityInspectorDashboard';
import PlannerDashboard from '@/components/dashboards/PlannerDashboard';
import UserDebugInfo from '@/components/UserDebugInfo';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  const { user } = useAuthStore();

  // 添加调试信息组件（临时）
  const showDebugInfo = true;

  // 根据用户角色显示不同的仪表板
  if (user && user.roles) {
    const userRoles = user.roles;

    // 操作员仪表板
    if (hasRole(userRoles, ROLES.OPERATOR) && !hasRole(userRoles, ROLES.ADMIN)) {
      return (
        <div>
          {showDebugInfo && <UserDebugInfo />}
          <OperatorDashboard />
        </div>
      );
    }

    // 质量检验员仪表板
    if (hasRole(userRoles, ROLES.QUALITY_INSPECTOR) && !hasRole(userRoles, ROLES.ADMIN)) {
      return (
        <div>
          {showDebugInfo && <UserDebugInfo />}
          <QualityInspectorDashboard />
        </div>
      );
    }

    // 生产计划员仪表板
    if (hasRole(userRoles, ROLES.PLANNER) && !hasRole(userRoles, ROLES.ADMIN)) {
      return (
        <div>
          {showDebugInfo && <UserDebugInfo />}
          <PlannerDashboard />
        </div>
      );
    }
  }

  // 默认仪表板（管理员、工艺工程师等）
  const { data: overview, isLoading: overviewLoading, error: overviewError } = useQuery(
    'dashboard-overview',
    () => apiClient.getDashboardOverview(),
    {
      refetchInterval: 30000, // Refresh every 30 seconds
      onSuccess: (data) => {
        console.log('Dashboard data loaded:', data);
      },
      onError: (error) => {
        console.error('Dashboard data loading error:', error);
      }
    }
  );

  const { data: productionSummary, isLoading: summaryLoading } = useQuery(
    'production-summary',
    () => apiClient.getProductionSummary(),
    { refetchInterval: 30000 }
  );

  // Work order data for charts
  const workOrderData = [
    { name: '待处理', value: overview?.work_order_status?.pending_orders || 0, color: '#ffa940' },
    { name: '进行中', value: overview?.work_order_status?.in_progress_orders || 0, color: '#1890ff' },
    { name: '已完成', value: overview?.work_order_status?.completed_orders || 0, color: '#52c41a' },
    { name: '已计划', value: overview?.work_order_status?.planned_orders || 0, color: '#722ed1' },
  ];

  const weeklyData = [
    { name: '周一', 完成任务: 12, 计划任务: 15 },
    { name: '周二', 完成任务: 19, 计划任务: 20 },
    { name: '周三', 完成任务: 8, 计划任务: 18 },
    { name: '周四', 完成任务: 15, 计划任务: 16 },
    { name: '周五', 完成任务: 22, 计划任务: 25 },
    { name: '周六', 完成任务: 18, 计划任务: 20 },
    { name: '周日', 完成任务: 10, 计划任务: 12 },
  ];

  if (overviewError) {
    return (
      <Alert
        message="数据加载失败"
        description="无法获取仪表板数据，请检查网络连接或稍后重试"
        type="error"
        showIcon
      />
    );
  }

  return (
    <div>
      {showDebugInfo && <UserDebugInfo />}

      <div className="page-header">
        <Title level={2} style={{ margin: 0 }}>
          生产仪表板
        </Title>
      </div>

      <Spin spinning={overviewLoading || summaryLoading}>
        {/* 调试信息和错误处理 */}
        {overviewError && (
          <Card title="❌ 数据加载错误" style={{ marginBottom: 16 }} size="small">
            <Alert
              message="仪表板数据加载失败"
              description={`错误信息: ${overviewError}`}
              type="error"
              showIcon
            />
          </Card>
        )}

        {overview && (
          <Card title="📊 调试信息" style={{ marginBottom: 16 }} size="small">
            <Alert
              message="数据加载成功"
              description={`设备利用率: ${overview.machine_status?.utilization_rate || 0}%, 质量合格率: ${overview.quality_metrics?.quality_rate || 0}%`}
              type="success"
              showIcon
            />
            <details style={{ marginTop: 10 }}>
              <summary style={{ cursor: 'pointer', color: '#1890ff' }}>查看原始数据</summary>
              <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto', marginTop: 10, background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
                {JSON.stringify(overview, null, 2)}
              </pre>
            </details>
          </Card>
        )}

        {!overview && !overviewLoading && !overviewError && (
          <Card title="⚠️ 无数据" style={{ marginBottom: 16 }} size="small">
            <Alert
              message="未获取到仪表板数据"
              description="请检查网络连接和API服务状态"
              type="warning"
              showIcon
            />
          </Card>
        )}

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总工单数"
                value={overview?.production_summary?.total_work_orders || 0}
                prefix={<ProjectOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="进行中工单"
                value={overview?.work_order_status?.in_progress_orders || 0}
                prefix={<FileTextOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="待处理任务"
                value={overview?.production_summary?.tasks_pending || 0}
                prefix={<CalendarOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="今日完成"
                value={overview?.production_summary?.tasks_completed_today || 0}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 效率指标 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} lg={8}>
            <Card>
              <Statistic
                title="设备利用率"
                value={overview?.machine_status?.utilization_rate || 0}
                suffix="%"
                prefix={<SettingOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card>
              <Statistic
                title="质量合格率"
                value={overview?.quality_metrics?.quality_rate || 0}
                suffix="%"
                prefix={<SafetyCertificateOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card>
              <Statistic
                title="生产效率"
                value={overview?.production_summary?.overall_efficiency || 0}
                suffix="%"
                prefix={<CheckCircleOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 图表 */}
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={16}>
            <Card title="本周任务完成情况" style={{ height: 400 }}>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={weeklyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="计划任务" fill="#91d5ff" />
                  <Bar dataKey="完成任务" fill="#1890ff" />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="工单状态分布" style={{ height: 400 }}>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={workOrderData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {workOrderData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default Dashboard;
